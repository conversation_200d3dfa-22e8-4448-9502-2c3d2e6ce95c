import { useState, useEffect, useCallback } from 'react';
import apiService from '../services/apiService';

/**
 * Custom hook for managing dashboard data
 * Handles fetching overview stats and results data
 */
export const useDashboard = (currentPage = 1, limit = 10) => {
  const [data, setData] = useState({
    results: [],
    overview: {
      summary: {
        total_assessments: 0,
        this_month: 0,
        success_rate: 0
      },
      recent_results: [],
      archetype_summary: {
        most_common: '',
        frequency: 0,
        last_archetype: '',
        unique_archetypes: 0,
        archetype_trend: ''
      }
    },
    pagination: {
      page: 1,
      limit: 10,
      total: 0,
      totalPages: 0
    }
  });

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  const fetchDashboardData = useCallback(async () => {
    try {
      setLoading(true);
      setError('');

      // Fetch overview and results data in parallel
      const [overviewResponse, resultsResponse] = await Promise.all([
        apiService.getStatsOverview(),
        apiService.getResults({ page: currentPage, limit })
      ]);

      const newData = { ...data };

      if (overviewResponse.success) {
        newData.overview = overviewResponse.data;
      }

      if (resultsResponse.success) {
        newData.results = resultsResponse.data.results || [];
        newData.pagination = resultsResponse.data.pagination || {
          page: currentPage,
          limit,
          total: 0,
          totalPages: 0
        };
      }

      setData(newData);

    } catch (err) {
      setError(err.response?.data?.message || 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  }, [currentPage, limit]);

  const deleteResult = useCallback(async (resultId) => {
    try {
      const response = await apiService.deleteResult(resultId);
      if (response.success) {
        // Remove from local state
        setData(prevData => ({
          ...prevData,
          results: prevData.results.filter(result => result.id !== resultId)
        }));
        
        // Refresh data to get updated stats
        await fetchDashboardData();
        return { success: true };
      }
    } catch (err) {
      const errorMessage = err.response?.data?.message || 'Failed to delete result';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  }, [fetchDashboardData]);

  const refreshData = useCallback(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  useEffect(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  return {
    data,
    loading,
    error,
    deleteResult,
    refreshData,
    setError
  };
};

export default useDashboard;
